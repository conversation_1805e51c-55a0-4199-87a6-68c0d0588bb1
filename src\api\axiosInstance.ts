import axios, {
  AxiosInstance,
  AxiosResponse,
  AxiosError,
  InternalAxiosRequestConfig,
  AxiosRequestConfig
} from 'axios';

// Type definitions
interface TokenResponse {
  access_token: string;
  refresh_token?: string;
}

interface RefreshTokenRequest {
  refresh_token: string;
}

// Extend Axios config with custom flags
interface CustomAxiosRequestConfig extends InternalAxiosRequestConfig {
  _retry?: boolean;
  skipAuth?: boolean;
}

// Create main API instance
const api: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://***********:8003'
});

// Separate auth API instance
const authApi: AxiosInstance = axios.create({
  baseURL: process.env.NEXT_PUBLIC_AUTH_API_BASE_URL || 'http://***********:8002'
});

// Helper functions
const isValidJWT = (token: string | null): token is string =>
  Boolean(token && token.split('.').length === 3);

const getAccessToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('access_token');
  }
  return null;
};

const getRefreshToken = (): string | null => {
  if (typeof window !== 'undefined') {
    return localStorage.getItem('refresh_token');
  }
  return null;
};

const setTokens = (accessToken: string, refreshToken?: string): void => {
  if (typeof window !== 'undefined') {
    localStorage.setItem('access_token', accessToken);
    if (refreshToken) {
      localStorage.setItem('refresh_token', refreshToken);
    }
  }
};

const clearTokens = (): void => {
  if (typeof window !== 'undefined') {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }
};

const redirectToLogin = (): void => {
  if (typeof window !== 'undefined') {
    window.location.href = '/';
  }
};

// Request interceptor
api.interceptors.request.use(
  (config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {
    const customConfig = config as CustomAxiosRequestConfig;

    if (customConfig.skipAuth) {
      return customConfig;
    }

    const token = getAccessToken();

    if (isValidJWT(token)) {
      customConfig.headers = customConfig.headers ?? {};
      customConfig.headers['Authorization'] = `Bearer ${token}`;
    } else if (token) {
      clearTokens();
    }

    if (!customConfig.url?.includes('/json/')) {
      customConfig.headers = customConfig.headers ?? {};
      customConfig.headers['Content-Type'] = 'application/json';
    }

    return customConfig;
  },
  (error: AxiosError) => Promise.reject(error)
);

// Response interceptor
api.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error: AxiosError) => {
    const originalRequest = error.config as CustomAxiosRequestConfig;

    if (originalRequest?._retry || originalRequest?.skipAuth) {
      return Promise.reject(error);
    }

    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      originalRequest._retry = true;

      try {
        const refreshToken = getRefreshToken();

        if (!refreshToken) {
          clearTokens();
          redirectToLogin();
          return Promise.reject(error);
        }

        const response = await authApi.post<TokenResponse>('/auth/refresh', {
          refresh_token: refreshToken
        } as RefreshTokenRequest);

        if (response.data?.access_token) {
          setTokens(response.data.access_token, response.data.refresh_token);

          if (originalRequest.headers) {
            originalRequest.headers['Authorization'] = `Bearer ${response.data.access_token}`;
          }

          return api(originalRequest);
        } else {
          throw new Error('Invalid token response');
        }
      } catch (refreshError) {
        console.error('Token refresh failed:', refreshError);
        clearTokens();
        redirectToLogin();
        return Promise.reject(refreshError);
      }
    }

    return Promise.reject(error);
  }
);

// Auth methods
export const authApiMethods = {
  login: async (credentials: { email: string; password: string }) => {
    return authApi.post<TokenResponse>('/auth/login', credentials);
  },

  signup: async (userData: {
    email: string;
    password: string;
    name?: string;
    [key: string]: any;
  }) => {
    return authApi.post<TokenResponse>('/auth/signup', userData);
  },

  forgotPassword: async (email: string) => {
    return authApi.post('/auth/forgot-password', { email });
  },

  resetPassword: async (token: string, newPassword: string) => {
    return authApi.post('/auth/reset-password', { token, password: newPassword });
  },

  verifyEmail: async (token: string) => {
    return authApi.post('/auth/verify-email', { token });
  }
};

// Authenticated methods
export const apiMethods = {
  get: <T = any>(url: string, config?: AxiosRequestConfig) =>
    api.get<T>(url, config as CustomAxiosRequestConfig),

  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    api.post<T>(url, data, config as CustomAxiosRequestConfig),

  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    api.put<T>(url, data, config as CustomAxiosRequestConfig),

  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    api.patch<T>(url, data, config as CustomAxiosRequestConfig),

  delete: <T = any>(url: string, config?: AxiosRequestConfig) =>
    api.delete<T>(url, config as CustomAxiosRequestConfig),

  // No-auth methods
  withoutAuth: {
    get: <T = any>(url: string, config?: AxiosRequestConfig) =>
      api.get<T>(url, { ...(config || {}), skipAuth: true } as CustomAxiosRequestConfig),

    post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
      api.post<T>(url, data, { ...(config || {}), skipAuth: true } as CustomAxiosRequestConfig)
  }
};

// Token utilities
export const tokenUtils = {
  getAccessToken,
  getRefreshToken,
  setTokens,
  clearTokens,
  isValidJWT,
  isAuthenticated: (): boolean => isValidJWT(getAccessToken())
};

export default api;
